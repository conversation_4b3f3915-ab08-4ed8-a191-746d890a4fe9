import { NextResponse } from "next/server";

// Mock tags data - in a real app, this would come from a database
const mockTags = [
  {
    id: "artificial-intelligence",
    name: "Artificial Intelligence",
    slug: "artificial-intelligence",
    articleCount: 25,
  },
  {
    id: "machine-learning",
    name: "Machine Learning", 
    slug: "machine-learning",
    articleCount: 20,
  },
  {
    id: "business-automation",
    name: "Business Automation",
    slug: "business-automation",
    articleCount: 15,
  },
  {
    id: "digital-innovation",
    name: "Digital Innovation",
    slug: "digital-innovation",
    articleCount: 18,
  },
  {
    id: "future-of-work",
    name: "Future of Work",
    slug: "future-of-work",
    articleCount: 12,
  },
  {
    id: "small-business",
    name: "Small Business",
    slug: "small-business",
    articleCount: 8,
  },
  {
    id: "cost-effective-solutions",
    name: "Cost-Effective Solutions",
    slug: "cost-effective-solutions",
    articleCount: 6,
  },
  {
    id: "implementation-guide",
    name: "Implementation Guide",
    slug: "implementation-guide",
    articleCount: 14,
  },
  {
    id: "customer-service",
    name: "Customer Service",
    slug: "customer-service",
    articleCount: 10,
  },
  {
    id: "automation",
    name: "Automation",
    slug: "automation",
    articleCount: 16,
  },
  {
    id: "ai-chatbots",
    name: "AI Chatbots",
    slug: "ai-chatbots",
    articleCount: 7,
  },
  {
    id: "support-systems",
    name: "Support Systems",
    slug: "support-systems",
    articleCount: 5,
  },
  {
    id: "ai-ethics",
    name: "AI Ethics",
    slug: "ai-ethics",
    articleCount: 9,
  },
  {
    id: "responsible-ai",
    name: "Responsible AI",
    slug: "responsible-ai",
    articleCount: 11,
  },
  {
    id: "best-practices",
    name: "Best Practices",
    slug: "best-practices",
    articleCount: 13,
  },
  {
    id: "data-science",
    name: "Data Science",
    slug: "data-science",
    articleCount: 12,
  },
  {
    id: "business-intelligence",
    name: "Business Intelligence",
    slug: "business-intelligence",
    articleCount: 8,
  },
  {
    id: "analytics",
    name: "Analytics",
    slug: "analytics",
    articleCount: 10,
  },
  {
    id: "leadership",
    name: "Leadership",
    slug: "leadership",
    articleCount: 6,
  },
  {
    id: "cloud-computing",
    name: "Cloud Computing",
    slug: "cloud-computing",
    articleCount: 9,
  },
  {
    id: "enterprise-solutions",
    name: "Enterprise Solutions",
    slug: "enterprise-solutions",
    articleCount: 7,
  },
  {
    id: "infrastructure",
    name: "Infrastructure",
    slug: "infrastructure",
    articleCount: 8,
  },
  {
    id: "optimization",
    name: "Optimization",
    slug: "optimization",
    articleCount: 11,
  },
];

export async function GET() {
  return NextResponse.json({
    tags: mockTags,
    total: mockTags.length,
  });
}
