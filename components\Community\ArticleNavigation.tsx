"use client";

import Link from "next/link";
import { useState, useEffect } from "react";
import { ChevronDown, Folder, Tag } from "lucide-react";
import { createTagSlug } from "@/lib/utils";

interface Category {
  id: string;
  name: string;
  slug: string;
  description: string;
  articleCount: number;
}

interface TagItem {
  id: string;
  name: string;
  slug: string;
  articleCount: number;
}

export default function ArticleNavigation() {
  const [categories, setCategories] = useState<Category[]>([]);
  const [popularTags, setPopularTags] = useState<TagItem[]>([]);
  const [showAllCategories, setShowAllCategories] = useState(false);
  const [showAllTags, setShowAllTags] = useState(false);

  useEffect(() => {
    // Fetch categories
    fetch('/api/categories')
      .then(res => res.json())
      .then(data => setCategories(data.categories || []))
      .catch(err => console.error('Error fetching categories:', err));

    // Fetch popular tags
    fetch('/api/tags')
      .then(res => res.json())
      .then(data => {
        // Sort by article count and take top tags
        const sortedTags = (data.tags || []).sort((a: TagItem, b: TagItem) => b.articleCount - a.articleCount);
        setPopularTags(sortedTags);
      })
      .catch(err => console.error('Error fetching tags:', err));
  }, []);

  const displayedCategories = showAllCategories ? categories : categories.slice(0, 6);
  const displayedTags = showAllTags ? popularTags : popularTags.slice(0, 12);

  return (
    <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-4 sm:p-6">
      {/* Categories Section */}
      <div className="mb-8">
        <div className="flex items-center gap-2 mb-4">
          <Folder className="w-5 h-5 text-blue-600" />
          <h3 className="text-lg font-semibold text-gray-900">Categories</h3>
        </div>
        
        <div className="space-y-2">
          {displayedCategories.length === 0 ? (
            <div className="text-sm text-gray-500 text-center py-4">
              Loading categories...
            </div>
          ) : (
            displayedCategories.map((category) => (
            <Link
              key={category.id}
              href={`/community/category/${category.slug}`}
              className="flex items-start justify-between p-3 rounded-lg hover:bg-gray-50 transition-colors group gap-3"
            >
              <div className="flex-1 min-w-0">
                <div className="font-medium text-gray-900 group-hover:text-blue-600 mb-1">
                  {category.name}
                </div>
                <div className="text-sm text-gray-500 leading-relaxed">
                  {category.description}
                </div>
              </div>
              <span className="text-sm text-gray-400 bg-gray-100 px-2 py-1 rounded-full flex-shrink-0">
                {category.articleCount}
              </span>
            </Link>
            ))
          )}
        </div>

        {categories.length > 6 && (
          <button
            onClick={() => setShowAllCategories(!showAllCategories)}
            className="flex items-center gap-1 text-sm text-blue-600 hover:text-blue-700 mt-3"
          >
            {showAllCategories ? 'Show Less' : 'Show All Categories'}
            <ChevronDown className={`w-4 h-4 transition-transform ${showAllCategories ? 'rotate-180' : ''}`} />
          </button>
        )}
      </div>

      {/* Popular Tags Section */}
      <div>
        <div className="flex items-center gap-2 mb-4">
          <Tag className="w-5 h-5 text-green-600" />
          <h3 className="text-lg font-semibold text-gray-900">Popular Tags</h3>
        </div>
        
        <div className="flex flex-wrap gap-2">
          {displayedTags.length === 0 ? (
            <div className="text-sm text-gray-500 text-center py-4 w-full">
              Loading tags...
            </div>
          ) : (
            displayedTags.map((tag) => (
            <Link
              key={tag.id}
              href={`/community/tag/${tag.slug}`}
              className="inline-flex items-center gap-1 px-3 py-1.5 bg-gray-100 hover:bg-gray-200 text-gray-700 text-sm rounded-full transition-colors"
            >
              {tag.name}
              <span className="text-xs text-gray-500">({tag.articleCount})</span>
            </Link>
            ))
          )}
        </div>

        {popularTags.length > 12 && (
          <button
            onClick={() => setShowAllTags(!showAllTags)}
            className="flex items-center gap-1 text-sm text-blue-600 hover:text-blue-700 mt-3"
          >
            {showAllTags ? 'Show Less' : 'Show All Tags'}
            <ChevronDown className={`w-4 h-4 transition-transform ${showAllTags ? 'rotate-180' : ''}`} />
          </button>
        )}
      </div>
    </div>
  );
}
