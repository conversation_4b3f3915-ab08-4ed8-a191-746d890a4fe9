import React from "react";
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON> } from "lucide-react";
import Image from "next/image";
import heroImage from "@/assets/Home/hero-ai.jpg";

export default function Hero() {
  return (
    <section className="relative min-h-screen flex items-center justify-center overflow-hidden">
      {/* Background Effects */}
      <div className="absolute inset-0 bg-ai-pattern opacity-50" />
      <div className="absolute top-1/4 left-1/4 w-96 h-96 bg-gradient-glow rounded-full blur-3xl opacity-30 animate-float" />
      <div
        className="absolute bottom-1/4 right-1/4 w-64 h-64 bg-gradient-glow rounded-full blur-3xl opacity-20 animate-float"
        style={{ animationDelay: "-3s" }}
      />

      <div className="relative z-10 max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-32">
        <div className="grid lg:grid-cols-2 gap-12 items-center">
          {/* Content */}
          <div className="space-y-8">
            <div className="space-y-6">
              <div className="flex items-center gap-2 text-sm font-medium text-primary">
                <Sparkles className="h-4 w-4" />
                AI-Powered Solutions
              </div>

              <h1 className="text-4xl md:text-6xl font-bold">
                Empowering Businesses with{" "}
                <span className="">AI & Marketing Technology</span>
              </h1>

              <p className="text-lg text-muted-foreground max-w-2xl">
                Transform your business operations and marketing strategies with
                cutting-edge AI solutions. From automation to analytics, we
                bridge the gap between technology and growth.
              </p>
            </div>

            <div className="flex flex-col sm:flex-row gap-4">
              <button className="flex items-center p-5 rounded-4xl">
                Discover Our Tools
                <ArrowRight className="h-4 w-4 ml-2 group-hover:translate-x-1 transition-transform" />
              </button>
              <button className="flex items-center p-5 rounded-4xl">
                <a
                  href="https://linkedin.com/company/kodaze"
                  target="_blank"
                  rel="noopener noreferrer"
                >
                  Join Our Community
                </a>
              </button>
            </div>

            {/* Features */}
            <div className="grid grid-cols-3 gap-6 pt-8 border-t border-border/50">
              <div className="text-center space-y-2">
                <Brain className="h-8 w-8 text-primary mx-auto" />
                <div className="text-sm font-medium">AI Automation</div>
              </div>
              <div className="text-center space-y-2">
                <Zap className="h-8 w-8 text-primary mx-auto" />
                <div className="text-sm font-medium">Smart Analytics</div>
              </div>
              <div className="text-center space-y-2">
                <Sparkles className="h-8 w-8 text-primary mx-auto" />
                <div className="text-sm font-medium">Growth Tools</div>
              </div>
            </div>
          </div>

          {/* Hero Image */}
          <div
            className="relative animate-slide-up"
            style={{ animationDelay: "0.2s" }}
          >
            <div className="relative z-10">
              <Image
                src={heroImage}
                alt="AI-powered business solutions"
                className="w-full h-auto rounded-2xl shadow-ai animate-glow"
              />
            </div>
            {/* Decorative elements */}
            <div className="absolute -top-4 -left-4 w-24 h-24 bg-gradient-primary rounded-full opacity-20 blur-xl" />
            <div className="absolute -bottom-4 -right-4 w-32 h-32 bg-gradient-hero rounded-full opacity-15 blur-xl" />
          </div>
        </div>
      </div>
    </section>
  );
}
