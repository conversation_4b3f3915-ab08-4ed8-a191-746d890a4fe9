import Image from "next/image";
import RelatedArticles from "@/components/Community/ArticleDetail/RelatedArticles";
import Tags from "@/components/Community/ArticleDetail/Tags";
import AuthorInfo from "@/components/Community/ArticleDetail/AuthorInfo";
import ContentAndControls from "@/components/Community/ArticleDetail/ContentAndControls";

const mockBlogPost = {
  id: "1",
  title:
    "The Future of AI in Business: A Comprehensive Guide to Digital Transformation",
  slug: "future-ai-business-digital-transformation",
  content: `
    <h2>Introduction to AI in Modern Business</h2>
    <p>Artificial Intelligence is revolutionizing the way businesses operate, from automating routine tasks to providing deep insights through data analysis. In this comprehensive guide, we'll explore how AI is reshaping industries and what it means for the future of work.</p>

    <h2>Key Benefits of AI Implementation</h2>
    <p>The implementation of AI technologies brings numerous advantages to businesses of all sizes. These benefits include increased efficiency, reduced operational costs, improved customer experiences, and enhanced decision-making capabilities.</p>

    <h3>Automation and Efficiency</h3>
    <p>AI-powered automation can handle repetitive tasks, freeing up human resources for more strategic work. This leads to significant productivity gains and allows employees to focus on creative and high-value activities.</p>

    <h3>Data-Driven Insights</h3>
    <p>Machine learning algorithms can analyze vast amounts of data to uncover patterns and trends that would be impossible for humans to detect manually. This enables businesses to make more informed decisions based on concrete evidence rather than intuition.</p>

    <h2>Real-World Applications</h2>
    <p>From chatbots providing 24/7 customer support to predictive analytics optimizing supply chains, AI applications are diverse and growing rapidly. Companies across industries are finding innovative ways to leverage AI for competitive advantage.</p>

    <h2>Challenges and Considerations</h2>
    <p>While AI offers tremendous opportunities, it also presents challenges including data privacy concerns, the need for skilled personnel, and potential job displacement. Organizations must carefully plan their AI adoption strategy to maximize benefits while mitigating risks.</p>

    <h2>Future Outlook</h2>
    <p>As AI technology continues to evolve, we can expect even more sophisticated applications and broader adoption across industries. The businesses that start their AI journey today will be best positioned to thrive in the digital future.</p>
  `,
  excerpt:
    "Discover how artificial intelligence is transforming modern business operations and learn practical strategies for successful AI implementation in your organization.",
  publishedAt: "2024-01-15",
  readingTime: 8,
  views: 12547,
  likes: 342,
  bookmarks: 89,
  author: {
    id: "1",
    name: "Dr. Sarah Chen",
    bio: "AI Research Director with 15+ years of experience in machine learning and business transformation. Former Google AI researcher, now helping companies navigate their digital transformation journey.",
    avatar: "https://i.pravatar.cc/150?u=sarah-chen",
    role: "AI Research Director",
    company: "Kodaze",
    location: "San Francisco, CA",
    experience: "15+ years",
    articles: 47,
    followers: 12500,
    verified: true,
    social: {
      twitter: "https://twitter.com/sarahchen",
      linkedin: "https://linkedin.com/in/sarahchen",
      website: "https://sarahchen.ai",
    },
    expertise: [
      "Machine Learning",
      "Business Strategy",
      "Digital Transformation",
      "AI Ethics",
    ],
  },
  category: "AI & Technology",
  tags: [
    "Artificial Intelligence",
    "Machine Learning",
    "Business Automation",
    "Digital Innovation",
    "Future of Work",
  ],
  featuredImage: "https://picsum.photos/seed/ai-business/1200/600",
  tableOfContents: [
    {
      id: "introduction",
      title: "Introduction to AI in Modern Business",
      level: 2,
    },
    { id: "benefits", title: "Key Benefits of AI Implementation", level: 2 },
    { id: "automation", title: "Automation and Efficiency", level: 3 },
    { id: "insights", title: "Data-Driven Insights", level: 3 },
    { id: "applications", title: "Real-World Applications", level: 2 },
    { id: "challenges", title: "Challenges and Considerations", level: 2 },
    { id: "future", title: "Future Outlook", level: 2 },
  ],
};

const mockRelatedPosts = [
  {
    id: "2",
    title: "Machine Learning Best Practices for Small Businesses",
    excerpt: "Learn how to implement ML solutions without breaking the bank",
    image: "https://picsum.photos/seed/ml-practices/400/250",
    readingTime: 6,
    publishedAt: "2024-01-10",
    author: "Michael Rodriguez",
  },
  {
    id: "3",
    title: "Building AI-Powered Customer Service Solutions",
    excerpt: "Transform your customer support with intelligent automation",
    image: "https://picsum.photos/seed/ai-customer/400/250",
    readingTime: 7,
    publishedAt: "2024-01-08",
    author: "Emily Johnson",
  },
  {
    id: "4",
    title: "The Ethics of AI: Responsible Implementation Guide",
    excerpt: "Navigate the ethical considerations of AI deployment",
    image: "https://picsum.photos/seed/ai-ethics/400/250",
    readingTime: 9,
    publishedAt: "2024-01-05",
    author: "Dr. James Wilson",
  },
];

export default function BlogPostPage() {
  return (
    <div className="min-h-screen bg-gray-50">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-20 grid lg:grid-cols-12 gap-8">
        {/* Main Content */}
        <article className="bg-white px-4 sm:px-6 md:px-8 py-8 lg:col-span-8">
          {/* Categories */}
          <span className="text-sm text-blue-600 font-medium">
            {mockBlogPost.category}
          </span>

          {/* Title */}
          <h1 className="text-3xl md:text-5xl font-bold text-gray-900 mb-8 leading-tight">
            {mockBlogPost.title}
          </h1>

          {/* Author Info - Responsive */}
          <AuthorInfo mockBlogPost={mockBlogPost} />

          {/* Featured Image */}
          <div className="relative w-full h-64 md:h-96 mb-8 rounded-lg overflow-hidden">
            <Image
              src={mockBlogPost.featuredImage}
              alt={mockBlogPost.title}
              fill
              className="object-cover"
              priority
            />
          </div>

          <ContentAndControls mockBlogPost={mockBlogPost} />

          <Tags mockBlogTags={mockBlogPost.tags} />
        </article>

        {/* Right Sidebar - Related Articles */}
        <RelatedArticles mockRelatedPosts={mockRelatedPosts} />
      </div>
    </div>
  );
}
