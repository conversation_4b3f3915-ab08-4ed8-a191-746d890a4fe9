import { Calendar } from "lucide-react";

function PrivacyHero() {
  return (
    <section className="pt-40 pb-16">
      <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8">
        <div className="text-center space-y-6">
          <h1 className="text-4xl md:text-5xl font-bold">
            Privacy <span className="">Policy</span>
          </h1>
          <p className="text-xl text-muted-foreground">
            Your privacy is important to us. This policy explains how we
            collect, use, and protect your personal information.
          </p>
          <div className="flex items-center justify-center gap-2 text-sm text-muted-foreground">
            <Calendar className="h-4 w-4" />
            <span>Last updated: August 25, 2025</span>
          </div>
        </div>
      </div>
    </section>
  );
}

export default PrivacyHero;
