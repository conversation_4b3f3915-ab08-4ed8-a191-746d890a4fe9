import { Clock, TrendingUp } from "lucide-react";
import Image from "next/image";
import Link from "next/link";
import React from "react";
import { mockRelatedPosts } from "./article-detail";


function RelatedArticles({ mockRelatedPosts }: { mockRelatedPosts: mockRelatedPosts }) {
  return (
    <aside className="lg:col-span-4">
      <div className="lg:sticky lg:top-24">
        <div className="bg-white rounded-xl p-6 shadow-sm border border-gray-100">
          <h3 className="text-lg font-semibold text-gray-900 mb-4 flex items-center gap-2">
            <TrendingUp className="w-5 h-5" />
            Related Articles
          </h3>
          <div className="space-y-4">
            {mockRelatedPosts.map((post) => (
              <Link
                key={post.id}
                href={`/community/${post.id}`}
                className="group block"
              >
                <div className="flex gap-3 p-3 rounded-lg hover:bg-gray-50 transition-colors">
                  <div className="relative w-16 h-16 bg-gray-100 rounded-lg overflow-hidden flex-shrink-0">
                    <Image
                      src={post.image}
                      alt={post.title}
                      fill
                      className="object-cover group-hover:scale-105 transition-transform duration-300"
                    />
                  </div>
                  <div className="flex-1 min-w-0">
                    <h4 className="font-medium text-gray-900 text-sm leading-tight mb-1 group-hover:text-blue-600 transition-colors line-clamp-2">
                      {post.title}
                    </h4>
                    <p className="text-xs text-gray-600 mb-2 line-clamp-2">
                      {post.excerpt}
                    </p>
                    <div className="flex items-center justify-between text-xs text-gray-500">
                      <span className="truncate">{post.author}</span>
                      <div className="flex items-center gap-1 flex-shrink-0">
                        <Clock className="w-3 h-3" />
                        {post.readingTime}m
                      </div>
                    </div>
                  </div>
                </div>
              </Link>
            ))}
          </div>
        </div>
      </div>
    </aside>
  );
}

export default RelatedArticles;
