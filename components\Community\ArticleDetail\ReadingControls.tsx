import { Minus, Plus, Printer, Volume2 } from "lucide-react";

function ReadingControls({ fontSize, setFontSize }: {
  fontSize: number;
  setFontSize: (size: number) => void;
}) {
  return (
    <div className="flex items-center justify-between mb-8 pb-4 border-b border-gray-100">
      <div className="flex items-center gap-3 text-sm text-gray-600">
        <span>Customize your reading</span>
      </div>
      <div className="flex items-center gap-3">
        {/* Font Size Controls */}
        <div className="flex items-center gap-1 bg-gray-50 rounded-lg px-2 py-1">
          <button
            onClick={() => setFontSize(Math.max(12, fontSize - 2))}
            className="w-7 h-7 flex items-center justify-center hover:bg-white rounded-md transition-colors"
            title="Decrease font size"
          >
            <Minus className="w-3 h-3 text-gray-600" />
          </button>
          <span className="text-sm font-medium w-6 text-center text-gray-700 select-none">
            {fontSize}
          </span>
          <button
            onClick={() => setFontSize(Math.min(24, fontSize + 2))}
            className="w-7 h-7 flex items-center justify-center hover:bg-white rounded-md transition-colors"
            title="Increase font size"
          >
            <Plus className="w-3 h-3 text-gray-600" />
          </button>
        </div>

        {/* Action Buttons */}
        <button
          className="w-8 h-8 flex items-center justify-center hover:bg-gray-50 rounded-lg transition-colors"
          title="Listen to article"
        >
          <Volume2 className="w-4 h-4 text-gray-600" />
        </button>
        <button
          className="w-8 h-8 flex items-center justify-center hover:bg-gray-50 rounded-lg transition-colors"
          title="Print article"
        >
          <Printer className="w-4 h-4 text-gray-600" />
        </button>
      </div>
    </div>
  );
}

export default ReadingControls;
