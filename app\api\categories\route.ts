import { NextResponse } from "next/server";

// Mock categories data - in a real app, this would come from a database
const mockCategories = [
  {
    id: "ai-technology",
    name: "AI & Technology",
    slug: "ai-technology",
    description: "Latest trends and insights in artificial intelligence and technology",
    articleCount: 15,
  },
  {
    id: "machine-learning",
    name: "Machine Learning",
    slug: "machine-learning", 
    description: "Deep dives into machine learning algorithms and applications",
    articleCount: 12,
  },
  {
    id: "customer-service",
    name: "Customer Service",
    slug: "customer-service",
    description: "Transforming customer experience with AI and automation",
    articleCount: 8,
  },
  {
    id: "ai-ethics",
    name: "AI Ethics",
    slug: "ai-ethics",
    description: "Responsible AI development and ethical considerations",
    articleCount: 6,
  },
  {
    id: "data-science",
    name: "Data Science",
    slug: "data-science",
    description: "Data analysis, visualization, and business intelligence",
    articleCount: 10,
  },
  {
    id: "cloud-computing",
    name: "Cloud Computing",
    slug: "cloud-computing",
    description: "Cloud infrastructure, deployment, and optimization strategies",
    articleCount: 7,
  },
];

export async function GET() {
  return NextResponse.json({
    categories: mockCategories,
    total: mockCategories.length,
  });
}
