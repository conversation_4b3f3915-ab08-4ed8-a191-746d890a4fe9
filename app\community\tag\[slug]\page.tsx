import { notFound } from "next/navigation";
import ArticlesList from "@/components/Community/Articles/ArticlesList";
import { Metadata } from "next";
import { Tag } from "lucide-react";

// This would typically come from your database or API
const tags = [
  {
    id: "artificial-intelligence",
    name: "Artificial Intelligence",
    slug: "artificial-intelligence",
    articleCount: 25,
  },
  {
    id: "machine-learning",
    name: "Machine Learning", 
    slug: "machine-learning",
    articleCount: 20,
  },
  {
    id: "business-automation",
    name: "Business Automation",
    slug: "business-automation",
    articleCount: 15,
  },
  {
    id: "digital-innovation",
    name: "Digital Innovation",
    slug: "digital-innovation",
    articleCount: 18,
  },
  {
    id: "future-of-work",
    name: "Future of Work",
    slug: "future-of-work",
    articleCount: 12,
  },
  {
    id: "small-business",
    name: "Small Business",
    slug: "small-business",
    articleCount: 8,
  },
  {
    id: "cost-effective-solutions",
    name: "Cost-Effective Solutions",
    slug: "cost-effective-solutions",
    articleCount: 6,
  },
  {
    id: "implementation-guide",
    name: "Implementation Guide",
    slug: "implementation-guide",
    articleCount: 14,
  },
  {
    id: "customer-service",
    name: "Customer Service",
    slug: "customer-service",
    articleCount: 10,
  },
  {
    id: "automation",
    name: "Automation",
    slug: "automation",
    articleCount: 16,
  },
  {
    id: "ai-chatbots",
    name: "AI Chatbots",
    slug: "ai-chatbots",
    articleCount: 7,
  },
  {
    id: "support-systems",
    name: "Support Systems",
    slug: "support-systems",
    articleCount: 5,
  },
  {
    id: "ai-ethics",
    name: "AI Ethics",
    slug: "ai-ethics",
    articleCount: 9,
  },
  {
    id: "responsible-ai",
    name: "Responsible AI",
    slug: "responsible-ai",
    articleCount: 11,
  },
  {
    id: "best-practices",
    name: "Best Practices",
    slug: "best-practices",
    articleCount: 13,
  },
  {
    id: "data-science",
    name: "Data Science",
    slug: "data-science",
    articleCount: 12,
  },
  {
    id: "business-intelligence",
    name: "Business Intelligence",
    slug: "business-intelligence",
    articleCount: 8,
  },
  {
    id: "analytics",
    name: "Analytics",
    slug: "analytics",
    articleCount: 10,
  },
  {
    id: "leadership",
    name: "Leadership",
    slug: "leadership",
    articleCount: 6,
  },
  {
    id: "cloud-computing",
    name: "Cloud Computing",
    slug: "cloud-computing",
    articleCount: 9,
  },
  {
    id: "enterprise-solutions",
    name: "Enterprise Solutions",
    slug: "enterprise-solutions",
    articleCount: 7,
  },
  {
    id: "infrastructure",
    name: "Infrastructure",
    slug: "infrastructure",
    articleCount: 8,
  },
  {
    id: "optimization",
    name: "Optimization",
    slug: "optimization",
    articleCount: 11,
  },
];

interface TagPageProps {
  params: {
    slug: string;
  };
}

export async function generateMetadata({ params }: TagPageProps): Promise<Metadata> {
  const tag = tags.find(t => t.slug === params.slug);
  
  if (!tag) {
    return {
      title: "Tag Not Found",
    };
  }

  return {
    title: `${tag.name} Articles | Kodaze Community`,
    description: `Explore articles tagged with ${tag.name}. Discover insights and best practices.`,
  };
}

export async function generateStaticParams() {
  return tags.map((tag) => ({
    slug: tag.slug,
  }));
}

async function fetchTagArticles(tagName: string) {
  try {
    const response = await fetch(
      `${process.env.NEXT_PUBLIC_BASE_URL || 'http://localhost:3000'}/api/articles?tag=${encodeURIComponent(tagName)}&limit=10`,
      { cache: 'no-store' }
    );
    
    if (!response.ok) {
      throw new Error('Failed to fetch articles');
    }
    
    const data = await response.json();
    return data.articles || [];
  } catch (error) {
    console.error('Error fetching tag articles:', error);
    return [];
  }
}

export default async function TagPage({ params }: TagPageProps) {
  const tag = tags.find(t => t.slug === params.slug);
  
  if (!tag) {
    notFound();
  }

  const articles = await fetchTagArticles(tag.name);

  return (
    <>
      {/* Hero Section with Tag Info */}
      <section className="bg-gradient-to-br from-gray-50 to-gray-100 py-20">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center">
            <div className="inline-flex items-center gap-2 px-4 py-2 bg-white rounded-full shadow-sm mb-6">
              <Tag className="w-5 h-5 text-gray-600" />
              <span className="text-sm text-gray-600 font-medium">Tag</span>
            </div>
            <h1 className="text-4xl md:text-6xl font-bold text-gray-900 mb-6">
              {tag.name}
            </h1>
            <p className="text-xl text-gray-600 mb-8 max-w-3xl mx-auto">
              Explore articles tagged with {tag.name}. Discover insights and best practices.
            </p>
            <div className="inline-flex items-center px-4 py-2 bg-white rounded-full shadow-sm">
              <span className="text-sm text-gray-600">
                {tag.articleCount} articles with this tag
              </span>
            </div>
          </div>
        </div>
      </section>

      {/* Articles Section */}
      <section className="py-16 max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <ArticlesList initialArticles={articles} />
      </section>
    </>
  );
}
