"use client";

import { Search } from "lucide-react";

function SearchSection({
  setQuery,
  query,
}: {
  setQuery: (query: string) => void;
  query: string;
}) {
  function handleSubmit(e: React.FormEvent<HTMLFormElement>) {
    e.preventDefault();
    const formData = new FormData(e.target as HTMLFormElement);
    const query = formData.get("q") as string;
    setQuery(query);
  }

  return (
    <section className="mb-8">
      <form
        onSubmit={handleSubmit}
        className="flex flex-col sm:flex-row gap-6 items-center"
      >
        <div className="relative w-full lg:w-96">
          <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-muted-foreground" />
          <input
            type="text"
            name="q"
            defaultValue={query}
            placeholder="Search articles, case studies, tools..."
            className="w-full rounded-md border border-input bg-background px-10 py-2 text-sm focus:outline-none"
          />
        </div>

        <button
          type="submit"
          className="px-4 py-2 border rounded-md bg-primary text-primary-foreground hover:bg-primary/90 transition"
        >
          Search
        </button>
      </form>
    </section>
  );
}

export default SearchSection;
