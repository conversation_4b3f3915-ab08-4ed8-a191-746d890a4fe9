import React from "react";
import Article from "@/common/components/Article";
import LatestArticlesTop from "./LatestArticlesTop";

function LatestArticles() {
  const article = {
    id: "1",
    title: "The Future of AI in Business",
    slug: "future-ai-business",
    excerpt: "Discover how AI is transforming modern business operations",
    image: "https://picsum.photos/seed/ai-business/1200/600",
    readingTime: 8,
    publishedAt: "2024-01-15",
    author: "Dr. <PERSON>",
    category: "AI & Technology",
    tags: [
      "Artificial Intelligence",
      "Machine Learning",
      "Business Automation",
      "Digital Innovation",
      "Future of Work",
    ],
    featuredImage: "https://picsum.photos/seed/ai-business/1200/600",
  };

  return (
    <section className="py-16">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <LatestArticlesTop />
        <div className="grid md:grid-cols-2 lg:grid-cols-3 gap-8">
          <Article article={article} />
          <Article article={article} />
          <Article article={article} />
        </div>
        <div className="mt-12 text-center">
          <button className="px-6 py-3 rounded-full bg-primary border font-semibold hover:scale-105 transform transition">
            View All Articles
          </button>
        </div>
      </div>
    </section>
  );
}

export default LatestArticles;
