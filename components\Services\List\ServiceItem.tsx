import { Feature } from "../services-types";

function ServiceItem({ f }: { f: Feature }) {
  return (
    <article
      className="group bg-white border border-slate-100 rounded-xl p-6 shadow-sm hover:shadow-md transition"
      aria-labelledby={`feature-${f.id}-title`}
    >
      <div className="flex items-start gap-4">
        <div className="flex-shrink-0">
          <div className="w-12 h-12 rounded-lg flex items-center justify-center bg-[#F3F4FF]">
            <span>{f.icon}</span>
          </div>
        </div>

        <div className="min-w-0">
          <h3
            id={`feature-${f.id}-title`}
            className="text-slate-900 text-lg font-semibold"
          >
            {f.title}
          </h3>
          <p className="mt-1 text-sm text-slate-500">{f.description}</p>
        </div>
      </div>
    </article>
  );
}

export default ServiceItem;
