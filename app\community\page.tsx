import Articles from "@/components/Community/Articles/Articles";
import ArticlesTop from "@/components/Community/Articles/ArticlesTop";
import CommunityHero from "@/components/Community/CommunityHero";
import ArticleNavigation from "@/components/Community/ArticleNavigation";
import { Suspense } from "react";

interface CommunityPageProps {
  searchParams: {
    category?: string;
    tag?: string;
    q?: string;
  };
}

async function fetchFilteredArticles(
  searchParams: CommunityPageProps["searchParams"]
) {
  try {
    const params = new URLSearchParams();
    if (searchParams.category) params.set("category", searchParams.category);
    if (searchParams.tag) params.set("tag", searchParams.tag);
    if (searchParams.q) params.set("q", searchParams.q);
    params.set("limit", "10");

    const response = await fetch(
      `${
        process.env.NEXT_PUBLIC_BASE_URL || "http://localhost:3000"
      }/api/articles?${params.toString()}`,
      { cache: "no-store" }
    );

    if (!response.ok) {
      throw new Error("Failed to fetch articles");
    }

    const data = await response.json();
    return data.articles || [];
  } catch (error) {
    console.error("Error fetching filtered articles:", error);
    return [];
  }
}

export default async function Community({ searchParams }: CommunityPageProps) {
  const articles = await fetchFilteredArticles(searchParams);
  const hasFilters =
    searchParams.category || searchParams.tag || searchParams.q;

  return (
    <>
      <CommunityHero />
      {!hasFilters && <ArticlesTop />}

      {/* Show filter info if filters are applied */}
      {hasFilters && (
        <section className="bg-gray-50 py-8">
          <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div className="text-center">
              <h2 className="text-2xl font-bold text-gray-900 mb-2">
                Filtered Articles
              </h2>
              <div className="flex flex-wrap justify-center gap-2">
                {searchParams.category && (
                  <span className="inline-flex items-center px-3 py-1 bg-blue-100 text-blue-800 text-sm rounded-full">
                    Category: {searchParams.category}
                  </span>
                )}
                {searchParams.tag && (
                  <span className="inline-flex items-center px-3 py-1 bg-green-100 text-green-800 text-sm rounded-full">
                    Tag: {searchParams.tag}
                  </span>
                )}
                {searchParams.q && (
                  <span className="inline-flex items-center px-3 py-1 bg-purple-100 text-purple-800 text-sm rounded-full">
                    Search: {searchParams.q}
                  </span>
                )}
              </div>
            </div>
          </div>
        </section>
      )}

      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        <div className="grid xl:grid-cols-4 gap-8">
          {/* Main Content */}
          <div className="xl:col-span-3">
            <Suspense fallback={<div className="text-center py-8">Loading articles...</div>}>
              <Articles initialArticles={articles} />
            </Suspense>
          </div>

          {/* Sidebar - Hidden on smaller screens, shown on xl and up */}
          <div className="hidden xl:block xl:col-span-1">
            <div className="sticky top-8">
              <ArticleNavigation />
            </div>
          </div>
        </div>

        {/* Mobile Navigation - Shown on smaller screens */}
        <div className="xl:hidden mt-8">
          <ArticleNavigation />
        </div>
      </div>
    </>
  );
}
