import { Calendar, Clock } from "lucide-react";

function ArticleSkeleton() {
  return (
    <article className="bg-white rounded-2xl shadow-md overflow-hidden border border-gray-100">
      {/* Image */}
      <div className="relative w-full h-44 sm:h-52 bg-gray-100 animate-pulse">
        <div className="w-full h-full bg-gray-300"></div>
      </div>

      <div className="p-5">
        {/* badge + date */}
        <div className="flex items-center justify-between gap-3 mb-3">
          <div className="flex gap-2 flex-wrap">
            <div className="w-16 h-5 bg-gray-300 rounded-full animate-pulse"></div>
            <div className="w-16 h-5 bg-gray-300 rounded-full animate-pulse"></div>
            <div className="w-16 h-5 bg-gray-300 rounded-full animate-pulse"></div>
          </div>

          <div className="flex items-center gap-2 text-xs text-gray-500">
            <Calendar size={14} />
            <div className="w-16 h-5 bg-gray-300 rounded-full animate-pulse"></div>
          </div>
        </div>

        {/* title */}
        <div className="w-3/4 h-6 bg-gray-300 rounded-full animate-pulse mb-3"></div>

        {/* excerpt */}
        <div className="w-1/2 h-5 bg-gray-300 rounded-full animate-pulse mb-4"></div>

        {/* tags */}
        <div className="flex flex-wrap gap-2 mb-4">
          <div className="w-20 h-5 bg-gray-300 rounded-full animate-pulse"></div>
          <div className="w-20 h-5 bg-gray-300 rounded-full animate-pulse"></div>
        </div>

        <div className="flex items-center justify-between text-sm text-gray-500">
          {/* author */}
          <div className="w-32 h-5 bg-gray-300 rounded-full animate-pulse"></div>

          {/* read time */}
          <div className="flex items-center gap-2">
            <Clock size={14} />
            <div className="w-16 h-5 bg-gray-300 rounded-full animate-pulse"></div>
          </div>
        </div>
      </div>
    </article>
  );
}

export default ArticleSkeleton;
