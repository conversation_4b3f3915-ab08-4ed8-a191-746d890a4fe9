import { notFound } from "next/navigation";
import ArticlesList from "@/components/Community/Articles/ArticlesList";
import CommunityHero from "@/components/Community/CommunityHero";
import { Metadata } from "next";

// This would typically come from your database or API
const categories = [
  {
    id: "ai-technology",
    name: "AI & Technology",
    slug: "ai-technology",
    description: "Latest trends and insights in artificial intelligence and technology",
    articleCount: 15,
  },
  {
    id: "machine-learning",
    name: "Machine Learning",
    slug: "machine-learning", 
    description: "Deep dives into machine learning algorithms and applications",
    articleCount: 12,
  },
  {
    id: "customer-service",
    name: "Customer Service",
    slug: "customer-service",
    description: "Transforming customer experience with AI and automation",
    articleCount: 8,
  },
  {
    id: "ai-ethics",
    name: "AI Ethics",
    slug: "ai-ethics",
    description: "Responsible AI development and ethical considerations",
    articleCount: 6,
  },
  {
    id: "data-science",
    name: "Data Science",
    slug: "data-science",
    description: "Data analysis, visualization, and business intelligence",
    articleCount: 10,
  },
  {
    id: "cloud-computing",
    name: "Cloud Computing",
    slug: "cloud-computing",
    description: "Cloud infrastructure, deployment, and optimization strategies",
    articleCount: 7,
  },
];

interface CategoryPageProps {
  params: {
    slug: string;
  };
}

export async function generateMetadata({ params }: CategoryPageProps): Promise<Metadata> {
  const category = categories.find(cat => cat.slug === params.slug);
  
  if (!category) {
    return {
      title: "Category Not Found",
    };
  }

  return {
    title: `${category.name} Articles | Kodaze Community`,
    description: category.description,
  };
}

export async function generateStaticParams() {
  return categories.map((category) => ({
    slug: category.slug,
  }));
}

async function fetchCategoryArticles(categoryName: string) {
  try {
    const response = await fetch(
      `${process.env.NEXT_PUBLIC_BASE_URL || 'http://localhost:3000'}/api/articles?category=${encodeURIComponent(categoryName)}&limit=10`,
      { cache: 'no-store' }
    );
    
    if (!response.ok) {
      throw new Error('Failed to fetch articles');
    }
    
    const data = await response.json();
    return data.articles || [];
  } catch (error) {
    console.error('Error fetching category articles:', error);
    return [];
  }
}

export default async function CategoryPage({ params }: CategoryPageProps) {
  const category = categories.find(cat => cat.slug === params.slug);
  
  if (!category) {
    notFound();
  }

  const articles = await fetchCategoryArticles(category.name);

  return (
    <>
      {/* Hero Section with Category Info */}
      <section className="bg-gradient-to-br from-blue-50 to-indigo-100 py-20">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center">
            <h1 className="text-4xl md:text-6xl font-bold text-gray-900 mb-6">
              {category.name}
            </h1>
            <p className="text-xl text-gray-600 mb-8 max-w-3xl mx-auto">
              {category.description}
            </p>
            <div className="inline-flex items-center px-4 py-2 bg-white rounded-full shadow-sm">
              <span className="text-sm text-gray-600">
                {category.articleCount} articles in this category
              </span>
            </div>
          </div>
        </div>
      </section>

      {/* Articles Section */}
      <section className="py-16 max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <ArticlesList initialArticles={articles} />
      </section>
    </>
  );
}
