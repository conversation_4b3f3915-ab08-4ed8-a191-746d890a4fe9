"use client";

import Image from "next/image";
import React, { useState } from "react";
import { mockBlogPost } from "./article-detail";
import { Share2 } from "lucide-react";

const formatDate = (dateString: string) => {
  return new Date(dateString).toLocaleDateString("en-US", {
    year: "numeric",
    month: "long",
    day: "numeric",
  });
};

type AuthorInfoProps = {
  mockBlogPost: mockBlogPost;
};

function AuthorInfo({ mockBlogPost }: AuthorInfoProps) {
  const [showShareMenu, setShowShareMenu] = useState(false);

  return (
    <div className="mb-8 pb-8 border-b border-gray-200">
      <div className="flex items-center justify-between gap-4">
        {/* Avatar + Info */}
        <div className="flex items-center gap-3">
          <Image
            src={mockBlogPost.author.avatar}
            alt={mockBlogPost.author.name}
            width={48}
            height={48}
            className="rounded-full"
          />
          <div className="flex-1">
            <div className="font-medium text-gray-900 text-sm sm:text-base">
              {mockBlogPost.author.name}
            </div>
            <div className="flex items-center gap-2 text-xs text-gray-600">
              <span>{formatDate(mockBlogPost.publishedAt)}</span>
              <span className="text-gray-500">•</span>
              <span>{mockBlogPost.readingTime} min read</span>
            </div>
          </div>
        </div>

        {/* Actions (Share, Like, etc.) */}
        <div className="flex items-center gap-3">
          <button
            onClick={() => setShowShareMenu(!showShareMenu)}
            className="flex items-center gap-1 px-3 py-1.5 bg-gray-100 text-gray-600 hover:bg-gray-200 rounded-full text-sm transition-colors"
          >
            <Share2 className="w-4 h-4" />
            Share
          </button>
        </div>
      </div>
    </div>
  );
}

export default AuthorInfo;
