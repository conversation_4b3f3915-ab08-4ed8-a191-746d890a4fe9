import { Calendar, Clock } from "lucide-react";
import Image from "next/image";
import Link from "next/link";
import type { ArticleItemType } from "@/components/Community/article-types";
import { createCategorySlug, createTagSlug, formatDate } from "@/lib/utils";

function Article({ article }: { article: ArticleItemType }) {
  return (
    <article className="bg-white rounded-2xl shadow-md overflow-hidden border border-gray-100">
      {/* Image */}
      <div className="relative w-full h-44 sm:h-52 bg-gray-100">
        <Image
          src={article.image}
          alt={article.title}
          fill
          sizes="(max-width: 640px) 100vw, 33vw"
          className="object-cover"
          priority={false}
        />
      </div>

      <div className="p-5">
        {/* category + date */}
        <div className="flex items-center justify-between gap-3 mb-3">
          <div className="flex gap-2 flex-wrap">
            <Link
              href={`/community/category/${createCategorySlug(article.category)}`}
              className="inline-block text-xs font-medium px-3 py-1 rounded-full bg-blue-100 text-blue-800 hover:bg-blue-200 transition-colors"
            >
              {article.category}
            </Link>
          </div>

          <div className="flex items-center gap-2 text-xs text-gray-500">
            <Calendar size={14} />
            <time dateTime={article.publishedAt}>
              {formatDate(article.publishedAt)}
            </time>
          </div>
        </div>

        {/* title */}
        <h3 className="text-gray-900 font-semibold text-lg leading-snug mb-3">
          <Link
            href={`/community/${article.slug}`}
            className="hover:text-blue-600 transition-colors"
          >
            {article.title}
          </Link>
        </h3>

        {/* excerpt */}
        <p className="text-sm text-gray-500 mb-4">{article.excerpt}</p>

        {/* tags */}
        <div className="flex flex-wrap gap-2 mb-4">
          {article.tags.slice(0, 3).map((tag) => (
            <Link
              key={tag}
              href={`/community/tag/${createTagSlug(tag)}`}
              className="text-xs px-2.5 py-1 rounded-full bg-gray-100 border border-transparent hover:bg-gray-200 transition-colors"
            >
              {tag}
            </Link>
          ))}
          {article.tags.length > 3 && (
            <span className="text-xs px-2.5 py-1 rounded-full bg-gray-100 text-gray-500">
              +{article.tags.length - 3} more
            </span>
          )}
        </div>

        <div className="flex items-center justify-between text-sm text-gray-500">
          {/* author */}
          <span className="truncate">{article.author}</span>

          {/* read time */}
          <div className="flex items-center gap-2">
            <Clock size={14} />
            <span>{article.readingTime} min read</span>
          </div>
        </div>
      </div>
    </article>
  );
}

export default Article;
