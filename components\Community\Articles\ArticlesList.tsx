"use client";

import Article from "@/common/components/Article";
import type { ArticleItemType } from "../article-types";
import { useEffect, useRef, useState } from "react";
import LoadMoreButton from "./LoadMoreButton";
import ArticleSkeleton from "./ArticleSkeleton";
import SearchSection from "../SearchSection";

function ArticlesList({
  initialArticles,
}: {
  initialArticles: ArticleItemType[];
}) {
  const [articles, setArticles] = useState<ArticleItemType[]>(initialArticles);
  const [hasMore, setHasMore] = useState(true);
  const [offset, setOffset] = useState(0);
  const [query, setQuery] = useState("");
  const [loading, setLoading] = useState(false);
  const isInit = useRef(true);

  useEffect(() => {
    async function loadArticles() {
      if (isInit.current) {
        isInit.current = false;
        return;
      }
      
      setLoading(true);
      try {
        const res = await fetch(
          `/api/articles?limit=3&offset=${offset}&q=${query}`
        );
        const data = await res.json();

        if (data.articles && data.articles.length === 0) {
          setHasMore(false);
        } else if (data.articles) {
          setArticles((prevArticles) => [...prevArticles, ...data.articles]);
          setOffset(offset + 3);
          setHasMore(data.hasMore);
        }
      } catch (error) {
        console.error("Error loading articles:", error);
        setHasMore(false);
      } finally {
        setLoading(false);
      }
    }
    loadArticles();
  }, [query, offset]);

  function increaseOffset() {
    setOffset(offset + 3);
  }

  return (
    <>
      <SearchSection setQuery={setQuery} query={query} />
      <div className="grid sm:grid-cols-1 md:grid-cols-2 xl:grid-cols-2 gap-6">
        {articles.map((article) => (
          <Article key={article.id} article={article} />
        ))}
        {articles.length === 0 && !loading && (
          <div className="col-span-full text-center py-12">
            <p className="text-gray-500 text-lg">No articles found</p>
            <p className="text-gray-400 text-sm mt-2">Try adjusting your search or browse our categories</p>
          </div>
        )}
        {loading &&
          Array.from({ length: 3 }, (_, k) => <ArticleSkeleton key={k} />)}
      </div>
      {hasMore && <LoadMoreButton increaseOffset={increaseOffset} />}
    </>
  );
}

export default ArticlesList;
