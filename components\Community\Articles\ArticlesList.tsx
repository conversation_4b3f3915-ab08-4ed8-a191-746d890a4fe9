"use client";

import Article from "@/common/components/Article";
import type { ArticleItemType } from "../article-types";
import { useEffect, useRef, useState } from "react";
import LoadMoreButton from "./LoadMoreButton";
import ArticleSkeleton from "./ArticleSkeleton";
import SearchSection from "../SearchSection";

function ArticlesList({
  initialArticles,
}: {
  initialArticles: ArticleItemType[];
}) {
  const [articles, setArticles] = useState<ArticleItemType[]>(initialArticles);
  const [hasMore, setHasMore] = useState(true);
  const [offset, setOffset] = useState(0);
  const [query, setQuery] = useState("");
  const [loading, setLoading] = useState(false);
  const isInit = useRef(true);

  useEffect(() => {
    async function loadArticles() {
      if (isInit.current) {
        isInit.current = false;
        return;
      }
      
      setLoading(true);
      try {
        const res = await fetch(
          `/api/articles?limit=3&offset=${offset}&q=${query}`
        );
        const newArticles = await res.json();
        if (newArticles.length === 0) {
          setHasMore(false);
        } else {
          setArticles((prevArticles) => [...prevArticles, ...newArticles]);
          setOffset(offset + 3);
        }
      } catch (error) {
        console.error("Error loading articles:", error);
      } finally {
        setLoading(false);
      }
    }
    loadArticles();
  }, [query, offset]);

  function increaseOffset() {
    setOffset(offset + 3);
  }

  return (
    <>
      <SearchSection setQuery={setQuery} query={query} />
      <div className="grid md:grid-cols-2 lg:grid-cols-3 gap-8">
        {articles.map((article) => (
          <Article key={article.id} article={article} />
        ))}
        {articles.length === 0 && <p>No articles found</p>}
        {loading &&
          Array.from({ length: 3 }, (_, k) => <ArticleSkeleton key={k} />)}
      </div>
      {hasMore && <LoadMoreButton increaseOffset={increaseOffset} />}
    </>
  );
}

export default ArticlesList;
