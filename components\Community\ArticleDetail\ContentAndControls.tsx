"use client";

import { useState } from "react";
import ReadingControls from "@/components/Community/ArticleDetail/ReadingControls";
import { mockBlogPost } from "./article-detail";

export default function ContentAndControls({
  mockBlogPost,
}: {
  mockBlogPost: mockBlogPost;
}) {
  const [fontSize, setFontSize] = useState(16);

  return (
    <>
      <ReadingControls fontSize={fontSize} setFontSize={setFontSize} />

      <div
        style={{ fontSize: `${fontSize}px` }}
        dangerouslySetInnerHTML={{ __html: mockBlogPost.content }}
      />
    </>
  );
}
