import { NextRequest, NextResponse } from "next/server";
import type { ArticleItemType } from "@/components/Community/article-types";

// Mock articles data - in a real app, this would come from a database
const mockArticles: ArticleItemType[] = [
  {
    id: "1",
    title: "The Future of AI in Business",
    slug: "future-ai-business",
    excerpt: "Discover how AI is transforming modern business operations",
    image: "https://picsum.photos/seed/ai-business/1200/600",
    readingTime: 8,
    publishedAt: "2024-01-15",
    author: "Dr. <PERSON>",
    category: "AI & Technology",
    tags: [
      "Artificial Intelligence",
      "Machine Learning",
      "Business Automation",
      "Digital Innovation",
      "Future of Work",
    ],
    featuredImage: "https://picsum.photos/seed/ai-business/1200/600",
  },
  {
    id: "2",
    title: "Machine Learning Best Practices for Small Businesses",
    slug: "ml-best-practices-small-business",
    excerpt: "Learn how to implement ML solutions without breaking the bank",
    image: "https://picsum.photos/seed/ml-practices/1200/600",
    readingTime: 6,
    publishedAt: "2024-01-10",
    author: "<PERSON>",
    category: "Machine Learning",
    tags: [
      "Machine Learning",
      "Small Business",
      "Cost-Effective Solutions",
      "Implementation Guide",
    ],
    featuredImage: "https://picsum.photos/seed/ml-practices/1200/600",
  },
  {
    id: "3",
    title: "Building AI-Powered Customer Service Solutions",
    slug: "ai-customer-service-solutions",
    excerpt: "Transform your customer support with intelligent automation",
    image: "https://picsum.photos/seed/ai-customer/1200/600",
    readingTime: 7,
    publishedAt: "2024-01-08",
    author: "Emily Johnson",
    category: "Customer Service",
    tags: [
      "Customer Service",
      "Automation",
      "AI Chatbots",
      "Support Systems",
    ],
    featuredImage: "https://picsum.photos/seed/ai-customer/1200/600",
  },
  {
    id: "4",
    title: "The Ethics of AI: Responsible Implementation Guide",
    slug: "ai-ethics-responsible-implementation",
    excerpt: "Navigate the ethical considerations of AI deployment",
    image: "https://picsum.photos/seed/ai-ethics/1200/600",
    readingTime: 9,
    publishedAt: "2024-01-05",
    author: "Dr. James Wilson",
    category: "AI Ethics",
    tags: [
      "AI Ethics",
      "Responsible AI",
      "Implementation Guide",
      "Best Practices",
    ],
    featuredImage: "https://picsum.photos/seed/ai-ethics/1200/600",
  },
  {
    id: "5",
    title: "Data Science Fundamentals for Business Leaders",
    slug: "data-science-fundamentals-business",
    excerpt: "Essential data science concepts every business leader should know",
    image: "https://picsum.photos/seed/data-science/1200/600",
    readingTime: 10,
    publishedAt: "2024-01-03",
    author: "Dr. Lisa Park",
    category: "Data Science",
    tags: [
      "Data Science",
      "Business Intelligence",
      "Analytics",
      "Leadership",
    ],
    featuredImage: "https://picsum.photos/seed/data-science/1200/600",
  },
  {
    id: "6",
    title: "Cloud Computing Strategies for Modern Enterprises",
    slug: "cloud-computing-strategies-enterprises",
    excerpt: "Optimize your cloud infrastructure for maximum efficiency",
    image: "https://picsum.photos/seed/cloud-computing/1200/600",
    readingTime: 12,
    publishedAt: "2024-01-01",
    author: "Alex Thompson",
    category: "Cloud Computing",
    tags: [
      "Cloud Computing",
      "Enterprise Solutions",
      "Infrastructure",
      "Optimization",
    ],
    featuredImage: "https://picsum.photos/seed/cloud-computing/1200/600",
  },
];

export async function GET(request: NextRequest) {
  const { searchParams } = new URL(request.url);
  
  // Get query parameters
  const limit = parseInt(searchParams.get("limit") || "10");
  const offset = parseInt(searchParams.get("offset") || "0");
  const query = searchParams.get("q") || "";
  const category = searchParams.get("category") || "";
  const tag = searchParams.get("tag") || "";

  // Filter articles based on search parameters
  let filteredArticles = mockArticles;

  // Filter by search query
  if (query) {
    filteredArticles = filteredArticles.filter(
      (article) =>
        article.title.toLowerCase().includes(query.toLowerCase()) ||
        article.excerpt.toLowerCase().includes(query.toLowerCase()) ||
        article.author.toLowerCase().includes(query.toLowerCase()) ||
        article.tags.some((t) => t.toLowerCase().includes(query.toLowerCase()))
    );
  }

  // Filter by category
  if (category) {
    filteredArticles = filteredArticles.filter(
      (article) => article.category.toLowerCase() === category.toLowerCase()
    );
  }

  // Filter by tag
  if (tag) {
    filteredArticles = filteredArticles.filter((article) =>
      article.tags.some((t) => t.toLowerCase() === tag.toLowerCase())
    );
  }

  // Sort by published date (newest first)
  filteredArticles.sort(
    (a, b) => new Date(b.publishedAt).getTime() - new Date(a.publishedAt).getTime()
  );

  // Apply pagination
  const paginatedArticles = filteredArticles.slice(offset, offset + limit);

  return NextResponse.json({
    articles: paginatedArticles,
    total: filteredArticles.length,
    hasMore: offset + limit < filteredArticles.length,
  });
}
