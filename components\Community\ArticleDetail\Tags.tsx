import Link from "next/link";
import React from "react";
import { Tag } from "lucide-react";
import { mockBlogPost } from "./article-detail";
import { createTagSlug } from "@/lib/utils";

function Tags({ mockBlogTags }: { mockBlogTags: mockBlogPost["tags"] }) {
  return (
    <div className="mt-8 pt-6 border-t border-gray-100">
      <h3 className="text-sm font-semibold text-gray-900 mb-3">Tags</h3>
      <div className="flex flex-wrap gap-2">
        {mockBlogTags.map((tag) => (
          <Link
            key={tag}
            href={`/community/tag/${createTagSlug(tag)}`}
            className="inline-flex items-center gap-1 px-3 py-1 bg-gray-100 hover:bg-gray-200 text-gray-700 text-sm rounded-full transition-colors"
          >
            <Tag className="w-3 h-3" />
            {tag}
          </Link>
        ))}
      </div>
    </div>
  );
}

export default Tags;
