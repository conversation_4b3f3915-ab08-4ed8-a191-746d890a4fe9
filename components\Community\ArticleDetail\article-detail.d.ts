export type mockRelatedPosts = {
  id: string;
  title: string;
  excerpt: string;
  image: string;
  readingTime: number;
  publishedAt: string;
  author: string;
}[];

export type mockBlogPost = {
  id: string;
  title: string;
  slug: string;
  content: string;
  excerpt: string;
  publishedAt: string;
  readingTime: number;
  views: number;
  likes: number;
  bookmarks: number;
  author: {
    id: string;
    name: string;
    bio: string;
    avatar: string;
    role: string;
    company: string;
    location: string;
    experience: string;
    articles: number;
    followers: number;
    verified: boolean;
    social: {
      twitter: string;
      linkedin: string;
      website: string;
    };
    expertise: string[];
  };
  category: string;
  tags: string[];
  featuredImage: string;
  tableOfContents: {
    id: string;
    title: string;
    level: number;
  }[];
};
