import ArticlesList from "./ArticlesList";
import type { ArticleItemType } from "../article-types";

interface ArticlesProps {
  initialArticles?: ArticleItemType[];
}

function Articles({ initialArticles }: ArticlesProps) {
  // Default articles if none provided
  const defaultArticles = [
    {
      id: "1",
      title: "The Future of AI in Business",
      slug: "future-ai-business",
      excerpt: "Discover how AI is transforming modern business operations",
      image: "https://picsum.photos/seed/ai-business/1200/600",
      readingTime: 8,
      publishedAt: "2024-01-15",
      author: "Dr. <PERSON>",
      category: "AI & Technology",
      tags: [
        "Artificial Intelligence",
        "Machine Learning",
        "Business Automation",
        "Digital Innovation",
        "Future of Work",
      ],
      featuredImage: "https://picsum.photos/seed/ai-business/1200/600",
    },
  ];

  const articles = initialArticles || defaultArticles;

  return <ArticlesList initialArticles={articles} />;
}

export default Articles;
